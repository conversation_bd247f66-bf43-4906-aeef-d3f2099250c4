import { useContext, useState, useEffect } from 'react';
import { ConfigContext } from "@/context/ConfigContext";
import { BusinessContext } from "@/context/BusinessContext";
import { getFromApi, postToApi, patchToApi } from "@/lib/api";
import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Plus, FileText } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { WorkOrderDetail } from "@/components/workOrders/WorkOrderDetails";
import { WorkOrderItem } from "@/components/workOrders/WorkOrderItem";
import { NewWorkOrderDialog } from '@/components/workOrders/NewWorkOrderDialog';
import { ISessionUser } from '@/types';

export function WorkOrdersPage() {
	const [workOrders, setWorkOrders] = useState([]);
	const [selectedWorkOrder, setSelectedWorkOrder] = useState(null);
	const [isNewWorkOrderDialogOpen, setIsNewWorkOrderDialogOpen] = useState(false);
	const { toast } = useToast();
	const { user } : { user: ISessionUser } = useContext(ConfigContext);
	const {
		provider,
		setupUser
	} = useContext(BusinessContext);

	useEffect(() => {
		setupUser(user)

		if (provider) {
			let url = `/api/v1/provider/${provider.id}/work_orders/90`;
			getFromApi(url, user, (response) => {
				console.log("WorkOrdersPage useEffect", response.result)
				let workorders = response.result || [];
				setWorkOrders(workorders.sort((a, b) => a.id - b.id));
			}, (error) => {
				console.error("Error fetching work orders:", error);
			});
		}
	}, [provider, user]);

	useEffect(() => {
		console.log("selected work order", selectedWorkOrder)
	}, [selectedWorkOrder])

	const updateWorkOrderStatus = (workOrder, newStatus) => {
		if (provider && user) {
			const url = `/api/v1/business/${provider.id}/work_order/${workOrder.id}`;
			console.log("url:", url)
			console.log("data sent:", { status: newStatus })
			patchToApi(url, user, { status: newStatus },
				(response) => {
					console.log("response:", response)
					const updatedWorkOrders = workOrders.map(wo =>
						wo.id === workOrder.id ? { ...wo, status: newStatus } : wo
					);
					setWorkOrders(updatedWorkOrders);
					setSelectedWorkOrder(prev => prev ? { ...prev, status: newStatus } : null);
					toast({
						title: "Status Updated",
						description: `Work order status changed to ${newStatus}`,
					});
				},
				(error) => {
					console.error("Error updating work order status:", error);
					toast({
						title: "Update Failed",
						description: "Could not update work order status",
						variant: "destructive",
					});
				}
			);
		}
	};

	const createInvoice = (workOrderId) => {
		if (provider && user) {
			const url = `/api/v1/invoice/${provider.id}/work_order/${workOrderId}/invoice`;
			const data = {}
			console.log("url:", url)
			console.log("data:", data)

			postToApi(url, user, data,
				(response) => {
					const statusUrl = `/api/v1/business/${provider.id}/work_order/${workOrderId}`;

					patchToApi(statusUrl, user, { status: 'invoiced' },
						(response) => {
							console.log("response:", response)
						},
						(error) => {
							console.error("Error updating work order status:", error);
						}
					);

					const updatedWorkOrders = workOrders.map(wo =>
						wo.id === workOrderId ? { ...wo, status: 'invoiced', invoice: response.result } : wo
					);
					setWorkOrders(updatedWorkOrders);
					setSelectedWorkOrder(prev => prev ? { ...prev, status: 'invoiced', invoice: response.result } : null);
					toast({
						title: "Invoice Created",
						description: `Invoice #${response.result.number} created successfully`,
					});
				},
				(error) => {
					console.error("Error creating invoice:", error);
					toast({
						title: "Invoice Creation Failed",
						description: "Could not create invoice from work order",
						variant: "destructive",
					});
				}
			);
		}
	};

	const updateSelectedWorkOrder = () => {
		let url = `/api/v1/work_order/${selectedWorkOrder.id} `;
		getFromApi(url, user, (response) => {
			const updatedWorkOrder = response.result || [];
			setSelectedWorkOrder(updatedWorkOrder);
		}, (error) => {
			console.error("Error fetching work orders:", error);
		});

	}

	return selectedWorkOrder ? (
		<WorkOrderDetail
			workOrder={selectedWorkOrder}
			onBack={() => { setSelectedWorkOrder(null) }}
			onStatusChange={updateWorkOrderStatus}
			onCreateInvoice={createInvoice}
			updateSelectedWorkOrder={updateSelectedWorkOrder}
		/>
	) : (
		<div className="space-y-6 p-6">
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-3xl font-bold">Work Orders</h1>
					<p className="text-muted-foreground">View and manage work orders.</p>
				</div>
				<Button 
					className="flex items-center gap-2"
					onClick={() => setIsNewWorkOrderDialogOpen(true)}
				>
					<Plus className="h-4 w-4" />
					New Work Order
				</Button>
			</div>

			{workOrders && workOrders.length > 0 ? (
				<div className="rounded-md border">
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>ID</TableHead>
								<TableHead>Customer</TableHead>
								<TableHead>Location</TableHead>
								<TableHead>Date</TableHead>
								<TableHead>Description</TableHead>
								<TableHead>Team</TableHead>
								<TableHead>Service Records</TableHead>
								<TableHead>Invoice</TableHead>
								<TableHead>Status</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{workOrders.map((workOrder) => (
								<WorkOrderItem
									key={workOrder.id}
									workOrder={workOrder}
									onSelect={() => setSelectedWorkOrder(workOrder)}
								/>
							))}
						</TableBody>
					</Table>
				</div>
			) : (
				<div className="text-center py-12 border rounded-md bg-muted/10">
					<div className="flex justify-center mb-4">
						<FileText className="h-12 w-12 text-muted-foreground" />
					</div>
					<h3 className="text-lg font-medium mb-2">No Work Orders Found</h3>
					<p className="text-muted-foreground mb-4">
						Create your first work order to start tracking service jobs.
					</p>
					<Button 
						className="flex items-center gap-2"
						onClick={() => setIsNewWorkOrderDialogOpen(true)}
					>
						<Plus className="h-4 w-4" />
						Create Work Order
					</Button>
				</div>
			)}

			<NewWorkOrderDialog 
				isOpen={isNewWorkOrderDialogOpen}
				setIsOpen={setIsNewWorkOrderDialogOpen} 
				workOrders={workOrders}
				setWorkOrders={setWorkOrders}
			/>
		</div>
	);
}

