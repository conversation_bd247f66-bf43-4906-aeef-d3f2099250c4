import { IRelatedUser, IRelatedLocation } from "./"
export interface ICustomerRes {
	id: number;
	userId: number;
	uid: string;
	uuid: string;
	name: string;
	email: string;
	phone: string;
	address: string;
	city: string;
	province: string;
	country: string;
	zipcode: string;
	latlng: string;
	description: string;
	type: string;
	currency: string;
	locale: string;
	rating: string;
	photo: string;
	banner: string;
	background: string;
	accountName: string;
	accountNumber: string;
	bank: string;
	sortCode: string;
	paymentTerms: string;
	taxId: string;
	nid: string;
	style: string;
	colour: string;
	summary: string;
	session: string;
	signal: string;
	enabled: boolean;
	facebook: string;
	instagram: string;
	twitter: string;
	youtube: string;
	telegram: string;
	whatsapp: string;
	website: string;
	flags: string[] | null;
	User: IRelatedUser;
	locations: IRelatedLocation[] | null;
	Contacts: any | null;
	Contractors: any | null;
	Configs: any | null;
	Categories: any | null;
	Roles: any | null;
	Subscriptions: any | null;
	Customers: any | null;
	CreatedAt: string;
	UpdatedAt: string;
	UpdatedBy: number;
	fields: ICustomField[];
}

export interface ICustomField {
  type: 'text' | 'number' | 'date' | 'select' | 'checkbox' | 'textarea';
  value: any;
  label: string;
  placeholder?: string;
  options: { label: string; value: any }[];
  required: boolean;
}