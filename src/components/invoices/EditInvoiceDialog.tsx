import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { MultiSelect } from "@/components/ui/multi-select";
import { Plus, Trash2, RotateCcw } from "lucide-react";
import { IInvoiceRes, IInvoiceItem } from '@/types';
// Enums for invoice status and payment methods
export enum InvoiceStatus {
  DRAFT = "draft",
  SENT = "sent",
  PAID = "paid",
  OVERDUE = "overdue"
}

export enum PaymentMethod {
  CASH = "cash",
  CHECK = "check",
  CREDIT_CARD = "credit_card",
  BANK_TRANSFER = "bank_transfer",
  OTHER = "other"
}

// Options arrays derived from enums
const INVOICE_STATUS_OPTIONS = [
  { label: "Draft", value: InvoiceStatus.DRAFT },
  { label: "Sent", value: InvoiceStatus.SENT },
  { label: "Paid", value: InvoiceStatus.PAID },
  { label: "Overdue", value: InvoiceStatus.OVERDUE }
];

const PAYMENT_METHOD_OPTIONS = [
  { label: "Cash", value: PaymentMethod.CASH },
  { label: "Check", value: PaymentMethod.CHECK },
  { label: "Credit Card", value: PaymentMethod.CREDIT_CARD },
  { label: "Bank Transfer", value: PaymentMethod.BANK_TRANSFER },
  { label: "Other", value: PaymentMethod.OTHER }
];

const FLAG_OPTIONS = [
  { label: "Urgent", value: "urgent" },
  { label: "Recurring", value: "recurring" },
  { label: "Created by Provider", value: "createdByProvider" },
  { label: "Paid", value: "paid" },
  { label: "Unpaid", value: "unpaid" },
  { label: "Overdue", value: "overdue" },
  { label: "Draft", value: "draft" }
];

interface EditInvoiceDialogProps {
  isOpen: boolean;
  onClose: (open: boolean) => void;
  invoice: IInvoiceRes;
  onSave: (updatedInvoice: Partial<IInvoiceRes>) => void;
}

export function EditInvoiceDialog({ isOpen, onClose, invoice, onSave }: EditInvoiceDialogProps) {
  const [formData, setFormData] = useState<IInvoiceRes>({} as IInvoiceRes);
  const [items, setItems] = useState<IInvoiceItem[]>([]);
  const [manualOverrides, setManualOverrides] = useState({
    subTotal: false,
    tax: false,
    total: false
  });

  const [itemChanges, setItemChanges] = useState<{
    created: IInvoiceItem[];
    updated: IInvoiceItem[];
    deleted: number[]; // Array of item IDs to delete
  }>({
    created: [],
    updated: [],
    deleted: []
  });

  const [isSaving, setIsSaving] = useState(false);
  const [originalItems, setOriginalItems] = useState<IInvoiceItem[]>([]);

  useEffect(() => {
    if (invoice) {
      setFormData({
        ...invoice,
        dueOn: invoice.dueOn ? new Date(invoice.dueOn).toISOString().split('T')[0] : '',
        paidOn: invoice.paidOn ? new Date(invoice.paidOn).toISOString().split('T')[0] : ''
      });

      const invoiceItems = invoice.items || [];
      setItems(invoiceItems);
      setOriginalItems(invoiceItems);

      // Reset item changes when loading new invoice
      setItemChanges({
        created: [],
        updated: [],
        deleted: []
      });
    }
  }, [invoice]);

  useEffect(() => {
    if (items.length > 0) {
      updateInvoiceTotals(items);
    }
  }, [items.length]);

  if (!invoice) return null;

  const handleFieldChange = (fieldName: string, value: string | number | boolean | string[]) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const handleManualOverride = (field: 'subTotal' | 'tax' | 'total', value: string) => {
    setManualOverrides(prev => ({ ...prev, [field]: true }));
    handleFieldChange(field, parseFloat(value) || 0);
  };

  const resetToCalculated = (field: 'subTotal' | 'tax' | 'total') => {
    setManualOverrides(prev => ({ ...prev, [field]: false }));
    const calculated = calculateFieldValue(field);
    handleFieldChange(field, calculated);

    // If resetting total, also recalculate it based on potentially updated subtotal/tax
    if (field === 'total') {
      setTimeout(() => {
        const newTotal = calculateFieldValue('total');
        handleFieldChange('total', newTotal);
      }, 0);
    }
  };

  const floatToPercentage = (value: number): number => {
    return Math.round((value || 0) * 100);
  };

  const percentageToFloat = (percentage: number): number => {
    return (percentage || 0) / 100;
  };

  const createInvoiceItem = async (invoiceId: number, item: Omit<IInvoiceItem, 'id' | 'invoiceId' | 'CreatedAt' | 'UpdatedAt'>): Promise<IInvoiceItem> => {
    const response = await fetch(`/api/v1/invoice/${invoiceId}/items`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(item),
    });

    if (!response.ok) {
      throw new Error(`Failed to create item: ${response.statusText}`);
    }

    return response.json();
  };

  const updateInvoiceItem = async (invoiceId: number, itemId: number, item: Partial<IInvoiceItem>): Promise<IInvoiceItem> => {
    const response = await fetch(`/api/v1/invoice/${invoiceId}/items/${itemId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(item),
    });

    if (!response.ok) {
      throw new Error(`Failed to update item: ${response.statusText}`);
    }

    return response.json();
  };

  const deleteInvoiceItem = async (invoiceId: number, itemId: number): Promise<void> => {
    const response = await fetch(`/api/v1/invoice/${invoiceId}/items/${itemId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error(`Failed to delete item: ${response.statusText}`);
    }
  };

  const calculateItemTaxAmount = (item: IInvoiceItem): number => {
    const baseAmount = (item.unitPrice || 0) * (item.quantity || 0) - (item.discountAmount || 0);
    return baseAmount * (item.taxRate || 0);
  };

  const calculateItemTotalPrice = (item: IInvoiceItem): number => {
    const baseAmount = (item.unitPrice || 0) * (item.quantity || 0);
    const taxAmount = calculateItemTaxAmount(item);
    return baseAmount - (item.discountAmount || 0) + taxAmount;
  };

  const calculateInvoiceTotals = {
    subTotal: (itemsList: IInvoiceItem[]): number => {
      return itemsList.reduce((sum, item) => sum + (item.totalPrice || 0), 0);
    },

    tax: (itemsList: IInvoiceItem[]): number => {
      return itemsList.reduce((sum, item) => sum + (item.taxAmount || 0), 0);
    },

    total: (itemsList: IInvoiceItem[], currentFormData: IInvoiceRes, overrides: any): number => {
      const subtotal = overrides.subTotal ? currentFormData.subTotal : calculateInvoiceTotals.subTotal(itemsList);
      const tax = overrides.tax ? currentFormData.tax : calculateInvoiceTotals.tax(itemsList);
      return subtotal + tax;
    }
  };

  const calculateFieldValue = (field: 'subTotal' | 'tax' | 'total'): number => {
    if (!items.length) return 0;

    switch (field) {
      case 'subTotal':
        return calculateInvoiceTotals.subTotal(items);
      case 'tax':
        return calculateInvoiceTotals.tax(items);
      case 'total':
        return calculateInvoiceTotals.total(items, formData, manualOverrides);
      default:
        return 0;
    }
  };

  const updateItemWithCalculations = (index: number, updatedItem: IInvoiceItem): IInvoiceItem[] => {
    const newItems = [...items];

    const itemWithCalculations = {
      ...updatedItem,
      taxAmount: calculateItemTaxAmount(updatedItem),
      totalPrice: calculateItemTotalPrice(updatedItem)
    };

    newItems[index] = itemWithCalculations;

    // Track the change if it's an existing item
    if (itemWithCalculations.id && itemWithCalculations.id > 0) {
      trackItemChange(itemWithCalculations, 'update');
    }

    return newItems;
  };

  // Update item without calculations (for non-calculation fields)
  const updateItemField = (index: number, field: keyof IInvoiceItem, value: any): IInvoiceItem[] => {
    const newItems = [...items];
    const updatedItem = { ...newItems[index], [field]: value };
    newItems[index] = updatedItem;

    // Track the change if it's an existing item
    if (updatedItem.id && updatedItem.id > 0) {
      trackItemChange(updatedItem, 'update');
    }

    return newItems;
  };

  const updateInvoiceTotals = (newItems: IInvoiceItem[]) => {
    const updates: Partial<IInvoiceRes> = {};

    // Only update fields that are not manually overridden
    if (!manualOverrides.subTotal)
      updates.subTotal = calculateInvoiceTotals.subTotal(newItems);

    if (!manualOverrides.tax)
      updates.tax = calculateInvoiceTotals.tax(newItems);

    if (!manualOverrides.total)
      updates.total = calculateInvoiceTotals.total(newItems, { ...formData, ...updates }, manualOverrides);

    if (Object.keys(updates).length > 0) {
      setFormData(prev => ({ ...prev, ...updates }));
    }
  };

  // Helper functions to track item changes
  const isNewItem = (item: IInvoiceItem): boolean => {
    return !item.id || item.id < 0; // New items have no ID or negative ID
  };

  const isItemModified = (item: IInvoiceItem, originalItem: IInvoiceItem): boolean => {
    const fieldsToCompare = ['description', 'sku', 'quantity', 'unit', 'unitPrice', 'discountAmount', 'taxRate'];
    return fieldsToCompare.some(field => item[field] !== originalItem[field]);
  };

  const trackItemChange = (item: IInvoiceItem, changeType: 'create' | 'update' | 'delete') => {
    setItemChanges(prev => {
      const newChanges = { ...prev };

      if (changeType === 'create') {
        // Add to created list if not already there
        if (!newChanges.created.find(i => i === item)) {
          newChanges.created.push(item);
        }
      } else if (changeType === 'update' && item.id) {
        // Add to updated list if not already there and not in created list
        if (!newChanges.created.find(i => i.id === item.id) &&
            !newChanges.updated.find(i => i.id === item.id)) {
          newChanges.updated.push(item);
        }
      } else if (changeType === 'delete' && item.id && item.id > 0) {
        // Add to deleted list if it's an existing item
        if (!newChanges.deleted.includes(item.id)) {
          newChanges.deleted.push(item.id);
        }
        // Remove from created/updated lists if present
        newChanges.created = newChanges.created.filter(i => i.id !== item.id);
        newChanges.updated = newChanges.updated.filter(i => i.id !== item.id);
      }

      return newChanges;
    });
  };

  const handleSave = async () => {
    if (isSaving) return;

    setIsSaving(true);

    try {
      // Step 1: Save the main invoice data (excluding items)
      const updatedInvoice: Omit<IInvoiceRes, 'items'> = {
        ...formData,
        dueOn: formData.dueOn ? new Date(formData.dueOn).toISOString() : invoice.dueOn,
        paidOn: formData.paidOn ? new Date(formData.paidOn).toISOString() : invoice.paidOn,
      };

      // Call the parent's onSave function with invoice data (without items)
      await onSave(updatedInvoice);

      // Step 2: Process all item changes sequentially
      await processItemChanges();

      // Step 3: Close dialog after successful save
      onClose(false);

    } catch (error) {
      console.error('Error saving invoice:', error);
      // TODO: Show error message to user
      alert(`Error saving invoice: ${error.message || 'Unknown error'}`);
    } finally {
      setIsSaving(false);
    }
  };

  const processItemChanges = async () => {
    const errors: string[] = [];

    try {
      // Process deletions first
      for (const itemId of itemChanges.deleted) {
        try {
          await deleteInvoiceItem(invoice.id, itemId);
        } catch (error) {
          errors.push(`Failed to delete item ${itemId}: ${error.message}`);
        }
      }

      // Process creations
      for (const item of itemChanges.created) {
        try {
          const itemData = {
            description: item.description,
            sku: item.sku,
            quantity: item.quantity,
            unit: item.unit,
            unitPrice: item.unitPrice,
            totalPrice: item.totalPrice,
            discountAmount: item.discountAmount,
            taxRate: item.taxRate,
            taxAmount: item.taxAmount
          };
          await createInvoiceItem(invoice.id, itemData);
        } catch (error) {
          errors.push(`Failed to create item "${item.description}": ${error.message}`);
        }
      }

      // Process updates
      for (const item of itemChanges.updated) {
        try {
          const itemData = {
            description: item.description,
            sku: item.sku,
            quantity: item.quantity,
            unit: item.unit,
            unitPrice: item.unitPrice,
            totalPrice: item.totalPrice,
            discountAmount: item.discountAmount,
            taxRate: item.taxRate,
            taxAmount: item.taxAmount
          };
          await updateInvoiceItem(invoice.id, item.id, itemData);
        } catch (error) {
          errors.push(`Failed to update item "${item.description}": ${error.message}`);
        }
      }

      if (errors.length > 0) {
        throw new Error(`Some item operations failed:\n${errors.join('\n')}`);
      }

    } catch (error) {
      throw new Error(`Item processing failed: ${error.message}`);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Invoice #{invoice.number || invoice.id}</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <Accordion type="multiple" defaultValue={["General", "DatesTerms", "Customer", "Items", "Financial", "Payment", "Provider", "Attachments"]} className="w-full">

            {/* 1. General Invoice Information */}
            <AccordionItem value="General">
              <AccordionTrigger className="text-lg font-semibold">
                General Invoice Information
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="name" className="text-right">
                      Invoice Name
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="name"
                        type="text"
                        value={formData.name || ''}
                        onChange={(e) => handleFieldChange('name', e.target.value)}
                        placeholder="Enter invoice name"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="number" className="text-right">
                      Invoice Number <span className="text-red-500 ml-1">*</span>
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="number"
                        type="text"
                        value={formData.number || ''}
                        placeholder="Enter invoice number"
                        required
                        readOnly
                        className='bg-muted text-muted-foreground'
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="status" className="text-right">
                      Status <span className="text-red-500 ml-1">*</span>
                    </Label>
                    <div className="col-span-3">
                      <Select
                        value={formData.status || InvoiceStatus.DRAFT}
                        onValueChange={(value) => handleFieldChange('status', value)}
                      >
                        <SelectTrigger id="status">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          {INVOICE_STATUS_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="flags" className="text-right">
                      Flags
                    </Label>
                    <div className="col-span-3">
                      <MultiSelect
                        options={FLAG_OPTIONS}
                        selected={Array.isArray(formData.flags) ? formData.flags : []}
                        onChange={(flags) => handleFieldChange('flags', flags)}
                        placeholder="Select flags..."
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="notes" className="text-right">
                      Notes
                    </Label>
                    <div className="col-span-3">
                      <Textarea
                        id="notes"
                        value={formData.notes || ''}
                        onChange={(e) => handleFieldChange('notes', e.target.value)}
                        placeholder="Enter notes"
                        rows={3}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="reference" className="text-right">
                      Reference
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="reference"
                        type="text"
                        value={formData.reference || ''}
                        onChange={(e) => handleFieldChange('reference', e.target.value)}
                        placeholder="Enter reference"
                      />
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 2. Dates and Terms */}
            <AccordionItem value="DatesTerms">
              <AccordionTrigger className="text-lg font-semibold">
                Dates and Terms
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="CreatedAt" className="text-right">
                      Created At
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="CreatedAt"
                        type="text"
                        value={invoice.CreatedAt ? new Date(invoice.CreatedAt).toLocaleString() : ''}
                        readOnly
                        className="bg-muted text-muted-foreground"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="UpdatedAt" className="text-right">
                      Updated At
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="UpdatedAt"
                        type="text"
                        value={invoice.UpdatedAt ? new Date(invoice.UpdatedAt).toLocaleString() : ''}
                        readOnly
                        className="bg-muted text-muted-foreground"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="dueOn" className="text-right">
                      Due Date <span className="text-red-500 ml-1">*</span>
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="dueOn"
                        type="date"
                        value={formData.dueOn || ''}
                        onChange={(e) => handleFieldChange('dueOn', e.target.value)}
                        required
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="paidOn" className="text-right">
                      Paid Date
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="paidOn"
                        type="date"
                        value={formData.paidOn || ''}
                        onChange={(e) => handleFieldChange('paidOn', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="paymentTerms" className="text-right">
                      Payment Terms
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="paymentTerms"
                        type="text"
                        value={formData.paymentTerms || ''}
                        onChange={(e) => handleFieldChange('paymentTerms', e.target.value)}
                        placeholder="Enter payment terms"
                      />
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 3. Customer/Recipient Information */}
            <AccordionItem value="Customer">
              <AccordionTrigger className="text-lg font-semibold">
                Customer/Recipient Information
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="email" className="text-right">
                      Email
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="email"
                        type="email"
                        value={formData.email || ''}
                        onChange={(e) => handleFieldChange('email', e.target.value)}
                        placeholder="Enter email"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="phone" className="text-right">
                      Phone
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="phone"
                        type="tel"
                        value={formData.phone || ''}
                        onChange={(e) => handleFieldChange('phone', e.target.value)}
                        placeholder="Enter phone"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="address" className="text-right">
                      Address
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="address"
                        type="text"
                        value={formData.address || ''}
                        onChange={(e) => handleFieldChange('address', e.target.value)}
                        placeholder="Enter address"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="city" className="text-right">
                      City
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="city"
                        type="text"
                        value={formData.city || ''}
                        onChange={(e) => handleFieldChange('city', e.target.value)}
                        placeholder="Enter city"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="province" className="text-right">
                      Province/State
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="province"
                        type="text"
                        value={formData.province || ''}
                        onChange={(e) => handleFieldChange('province', e.target.value)}
                        placeholder="Enter province/state"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="country" className="text-right">
                      Country
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="country"
                        type="text"
                        value={formData.country || ''}
                        onChange={(e) => handleFieldChange('country', e.target.value)}
                        placeholder="Enter country"
                      />
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 4. Invoice Items */}

            <AccordionItem value="Items">
              <AccordionTrigger className="text-lg font-semibold">
                Invoice Items
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4 py-4">
                  {items.map((item, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-4">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium">Item {index + 1}</h4>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const itemToDelete = items[index];
                            const newItems = items.filter((_, i) => i !== index);

                            // Track the deletion if it's an existing item
                            if (itemToDelete.id && itemToDelete.id > 0) {
                              trackItemChange(itemToDelete, 'delete');
                            }

                            setItems(newItems);
                            updateInvoiceTotals(newItems);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>Description</Label>
                          <Input
                            value={item.description || ''}
                            onChange={(e) => {
                              const newItems = updateItemField(index, 'description', e.target.value);
                              setItems(newItems);
                            }}
                          />
                        </div>
                        <div>
                          <Label>SKU</Label>
                          <Input
                            value={item.sku || ''}
                            onChange={(e) => {
                              const newItems = updateItemField(index, 'sku', e.target.value);
                              setItems(newItems);
                            }}
                          />
                        </div>
                        <div>
                          <Label>Quantity</Label>
                          <Input
                            type="number"
                            value={item.quantity || 0}
                            onChange={(e) => {
                              const updatedItem = { ...item, quantity: parseFloat(e.target.value) || 0 };
                              const newItems = updateItemWithCalculations(index, updatedItem);
                              setItems(newItems);
                              updateInvoiceTotals(newItems);
                            }}
                          />
                        </div>
                        <div>
                          <Label>Unit</Label>
                          <Input
                            value={item.unit || ''}
                            onChange={(e) => {
                              const newItems = updateItemField(index, 'unit', e.target.value);
                              setItems(newItems);
                              // Unit doesn't affect calculations, no need to update totals
                            }}
                          />
                        </div>
                        <div>
                          <Label>Unit Price</Label>
                          <Input
                            type="number"
                            step="0.01"
                            value={item.unitPrice || 0}
                            onChange={(e) => {
                              const updatedItem = { ...item, unitPrice: parseFloat(e.target.value) || 0 };
                              const newItems = updateItemWithCalculations(index, updatedItem);
                              setItems(newItems);
                              updateInvoiceTotals(newItems);
                            }}
                          />
                        </div>
                        <div>
                          <Label>Discount Amount</Label>
                          <Input
                            type="number"
                            step="0.01"
                            value={item.discountAmount || 0}
                            onChange={(e) => {
                              const updatedItem = { ...item, discountAmount: parseFloat(e.target.value) || 0 };
                              const newItems = updateItemWithCalculations(index, updatedItem);
                              setItems(newItems);
                              updateInvoiceTotals(newItems);
                            }}
                          />
                        </div>
                        <div>
                          <Label>Tax Rate (%)</Label>
                          <div className="relative">
                            <Input
                              type="number"
                              step="1"
                              min="0"
                              max="100"
                              value={floatToPercentage(item.taxRate)}
                              onChange={(e) => {
                                const percentageValue = parseInt(e.target.value) || 0;
                                const updatedItem = { ...item, taxRate: percentageToFloat(percentageValue) };
                                const newItems = updateItemWithCalculations(index, updatedItem);
                                setItems(newItems);
                                updateInvoiceTotals(newItems);
                              }}
                              placeholder="0"
                              className="pr-8"
                            />
                            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
                              %
                            </span>
                          </div>
                        </div>
                        <div>
                          <Label>Tax Amount</Label>
                          <div className="relative">
                            <Input
                              type="number"
                              step="0.01"
                              value={item.taxAmount || 0}
                              onChange={(e) => {
                                const newItems = [...items];
                                newItems[index] = { ...item, taxAmount: parseFloat(e.target.value) || 0 };
                                setItems(newItems);
                                updateInvoiceTotals(newItems);
                              }}
                              className="pr-8"
                            />
                            <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">
                              Auto
                            </span>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            Calculated automatically from tax rate
                          </p>
                        </div>
                        <div className="col-span-2">
                          <Label>Total Price</Label>
                          <div className="relative">
                            <Input
                              type="number"
                              step="0.01"
                              value={item.totalPrice || 0}
                              onChange={(e) => {
                                const newItems = [...items];
                                newItems[index] = { ...item, totalPrice: parseFloat(e.target.value) || 0 };
                                setItems(newItems);
                                updateInvoiceTotals(newItems);
                              }}
                              className="pr-8"
                            />
                            <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">
                              Auto
                            </span>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            Calculated automatically from price, quantity, discount, and tax
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    onClick={() => {
                      const newItem = {
                        id: -(Date.now()), // Negative ID for new items
                        invoiceId: invoice.id,
                        description: '',
                        sku: '',
                        quantity: 1,
                        unit: '',
                        unitPrice: 0,
                        discountAmount: 0,
                        taxRate: 0,
                        taxAmount: 0,
                        totalPrice: 0,
                        CreatedAt: new Date().toISOString(),
                        UpdatedAt: new Date().toISOString()
                      } as IInvoiceItem;

                      // Track as new item
                      trackItemChange(newItem, 'create');

                      const newItems = [...items, newItem];
                      setItems(newItems);
                      updateInvoiceTotals(newItems);
                    }}
                    className="w-full"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Item
                  </Button>
                </div>
              </AccordionContent>
            </AccordionItem>


            {/* 5. Financial Summary */}
            <AccordionItem value="Financial">
              <AccordionTrigger className="text-lg font-semibold">
                Financial Summary
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="subTotal" className="text-right font-medium">
                      Subtotal
                      {manualOverrides.subTotal && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => resetToCalculated('subTotal')}
                          className="ml-2 h-6 w-6 p-0"
                          title="Reset to calculated value"
                        >
                          <RotateCcw className="h-3 w-3" />
                        </Button>
                      )}
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="subTotal"
                        type="number"
                        step="0.01"
                        value={formData.subTotal || 0}
                        onChange={(e) => handleManualOverride('subTotal', e.target.value)}
                        placeholder="0.00"
                        className={manualOverrides.subTotal ? "border-orange-500 bg-orange-50 dark:bg-orange-950" : ""}
                      />
                      {manualOverrides.subTotal ? (
                        <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                          Manual override active
                        </p>
                      ) : (
                        <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                          Automatically calculated from items
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="tax" className="text-right font-medium">
                      Tax Amount
                      {manualOverrides.tax && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => resetToCalculated('tax')}
                          className="ml-2 h-6 w-6 p-0"
                          title="Reset to calculated value"
                        >
                          <RotateCcw className="h-3 w-3" />
                        </Button>
                      )}
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="tax"
                        type="number"
                        step="0.01"
                        value={formData.tax || 0}
                        onChange={(e) => handleManualOverride('tax', e.target.value)}
                        placeholder="0.00"
                        className={manualOverrides.tax ? "border-orange-500 bg-orange-50 dark:bg-orange-950" : ""}
                      />
                      {manualOverrides.tax ? (
                        <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                          Manual override active
                        </p>
                      ) : (
                        <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                          Automatically calculated from items
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="total" className="text-right font-medium">
                      Total
                      {manualOverrides.total && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => resetToCalculated('total')}
                          className="ml-2 h-6 w-6 p-0"
                          title="Reset to calculated value"
                        >
                          <RotateCcw className="h-3 w-3" />
                        </Button>
                      )}
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="total"
                        type="number"
                        step="0.01"
                        value={formData.total || 0}
                        onChange={(e) => handleManualOverride('total', e.target.value)}
                        placeholder="0.00"
                        className={manualOverrides.total ? "border-orange-500 bg-orange-50 dark:bg-orange-950" : ""}
                      />
                      {manualOverrides.total ? (
                        <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                          Manual override active
                        </p>
                      ) : (
                        <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                          Automatically calculated (subtotal + tax)
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="taxRate" className="text-right">
                      Tax Rate (%)
                    </Label>
                    <div className="col-span-3">
                      <div className="relative">
                        <Input
                          id="taxRate"
                          type="number"
                          step="1"
                          min="0"
                          max="100"
                          value={floatToPercentage(formData.taxRate)}
                          onChange={(e) => {
                            const percentageValue = parseInt(e.target.value) || 0;
                            handleFieldChange('taxRate', percentageToFloat(percentageValue));
                          }}
                          placeholder="0"
                          className="pr-8"
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
                          %
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Enter as percentage (e.g., 15 for 15%)
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="discountType" className="text-right">
                      Discount Type
                    </Label>
                    <div className="col-span-3">
                      <Select
                        value={formData.discountType || 'amount'}
                        onValueChange={(value) => handleFieldChange('discountType', value)}
                      >
                        <SelectTrigger id="discountType">
                          <SelectValue placeholder="Select discount type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="amount">Fixed Amount</SelectItem>
                          <SelectItem value="percentage">Percentage</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="discountValue" className="text-right">
                      Discount Value {formData.discountType === 'percentage' ? '(%)' : ''}
                    </Label>
                    <div className="col-span-3">
                      {formData.discountType === 'percentage' ? (
                        <div className="relative">
                          <Input
                            id="discountValue"
                            type="number"
                            step="1"
                            min="0"
                            max="100"
                            value={floatToPercentage(formData.discountValue)}
                            onChange={(e) => {
                              const percentageValue = parseInt(e.target.value) || 0;
                              handleFieldChange('discountValue', percentageToFloat(percentageValue));
                            }}
                            placeholder="0"
                            className="pr-8"
                          />
                          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
                            %
                          </span>
                        </div>
                      ) : (
                        <Input
                          id="discountValue"
                          type="number"
                          step="0.01"
                          value={formData.discountValue || 0}
                          onChange={(e) => handleFieldChange('discountValue', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      )}
                      <p className="text-xs text-muted-foreground mt-1">
                        {formData.discountType === 'percentage'
                          ? 'Enter as percentage (e.g., 10 for 10%)'
                          : 'Enter as fixed amount'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 6. Payment Information */}
            <AccordionItem value="Payment">
              <AccordionTrigger className="text-lg font-semibold">
                Payment Information
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="Paid" className="text-right">
                      Paid
                    </Label>
                    <div className="col-span-3">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="Paid"
                          checked={formData.Paid === true}
                          onCheckedChange={(checked) => handleFieldChange('Paid', checked)}
                        />
                        <Label htmlFor="Paid" className="text-sm font-normal">
                          Mark as paid
                        </Label>
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="paymentMethod" className="text-right">
                      Payment Method
                    </Label>
                    <div className="col-span-3">
                      <Select
                        value={formData.paymentMethod || ''}
                        onValueChange={(value) => handleFieldChange('paymentMethod', value)}
                      >
                        <SelectTrigger id="paymentMethod">
                          <SelectValue placeholder="Select payment method" />
                        </SelectTrigger>
                        <SelectContent>
                          {PAYMENT_METHOD_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="paidBy" className="text-right">
                      Paid By
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="paidBy"
                        type="text"
                        value={formData.paidBy || ''}
                        onChange={(e) => handleFieldChange('paidBy', e.target.value)}
                        placeholder="Enter who paid"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="accountName" className="text-right">
                      Account Name
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="accountName"
                        type="text"
                        value={formData.accountName || ''}
                        onChange={(e) => handleFieldChange('accountName', e.target.value)}
                        placeholder="Enter account name"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="accountNumber" className="text-right">
                      Account Number
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="accountNumber"
                        type="text"
                        value={formData.accountNumber || ''}
                        onChange={(e) => handleFieldChange('accountNumber', e.target.value)}
                        placeholder="Enter account number"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="sortCode" className="text-right">
                      Sort Code
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="sortCode"
                        type="text"
                        value={formData.sortCode || ''}
                        onChange={(e) => handleFieldChange('sortCode', e.target.value)}
                        placeholder="Enter sort code"
                      />
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 7. Provider Information */}
            <AccordionItem value="Provider">
              <AccordionTrigger className="text-lg font-semibold">
                Provider Information
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="providerId" className="text-right">
                      Provider ID
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="providerId"
                        type="number"
                        value={formData.providerId || 0}
                        readOnly
                        className='bg-muted text-muted-foreground'
                      />
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 8. Attachments and Custom Data */}
            <AccordionItem value="Attachments">
              <AccordionTrigger className="text-lg font-semibold">
                Attachments and Custom Data
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="attachments" className="text-right">
                      Attachments
                    </Label>
                    <div className="col-span-3">
                      <Textarea
                        id="attachments"
                        value={formData.attachments ? JSON.stringify(formData.attachments, null, 2) : ''}
                        onChange={(e) => {
                          try {
                            const parsed = JSON.parse(e.target.value);
                            handleFieldChange('attachments', parsed);
                          } catch {
                            handleFieldChange('attachments', e.target.value);
                          }
                        }}
                        placeholder="Enter attachments data (JSON format)"
                        rows={3}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="data" className="text-right">
                      Custom Data
                    </Label>
                    <div className="col-span-3">
                      <Textarea
                        id="data"
                        value={formData.data ? JSON.stringify(formData.data, null, 2) : ''}
                        onChange={(e) => {
                          try {
                            const parsed = JSON.parse(e.target.value);
                            handleFieldChange('data', parsed);
                          } catch {
                            handleFieldChange('data', e.target.value);
                          }
                        }}
                        placeholder="Enter custom data (JSON format)"
                        rows={3}
                      />
                    </div>
                  </div>

                  {/* Custom Fields Placeholder */}
                  <div className="border-t pt-4 mt-4">
                    <h4 className="text-sm font-medium mb-3">Custom Fields</h4>
                    <div className="text-sm text-muted-foreground p-4 border rounded-lg bg-muted/20">
                      <p>Custom fields functionality will be implemented here.</p>
                      <p className="mt-1">This section will support dynamic custom fields based on customer and business configurations.</p>
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

          </Accordion>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onClose(false)} disabled={isSaving}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}