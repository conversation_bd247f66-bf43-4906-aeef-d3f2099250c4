import { useEffect, useMemo, useState } from "react";
import { Search, MapPin } from "lucide-react";
import { Input } from "@/components/ui/input";
import { LocationList } from "./LocationList";
import { IRelatedLocation } from "@/types";

/**
 * SearchableLocationList - A reusable component that wraps LocationList with search functionality
 *
 * @param {Array} locations - Array of location objects to display and search through
 * @param {Function} onLocationClick - Callback function when a location is clicked
 * @returns {JSX.Element} Searchable location list component
 */


interface SearchableLocationListProps {
  locations: IRelatedLocation[];
  onLocationClick: (location: IRelatedLocation) => void;
}
export function SearchableLocationList({ locations, onLocationClick }: SearchableLocationListProps): JSX.Element {
  const [searchTerm, setSearchTerm] = useState("");
  const [posibleStates, setPossibleStates] = useState([]);
  const [selectedState, setSelectedState] = useState(null);

  useEffect(() => {
    if (locations && locations.length > 0) {
      let states = locations.map((loc) => loc.province);
      states = [...new Set(states)];
      setPossibleStates(states);
    }
  }, [locations]);

  const filteredLocations = useMemo(() => {
    if (!locations || locations.length === 0) return [];

    if (searchTerm.length < 2) return locations;

    const searchLower = searchTerm.toLowerCase();

    return locations.filter((loc) => {
      if (loc.name && loc.name.toLowerCase().includes(searchLower))
        return true;

      if (loc.address && loc.address.toLowerCase().includes(searchLower))
        return true;

      if (loc.city && loc.city.toLowerCase().includes(searchLower))
        return true;

      if (loc.contactName && loc.contactName.toLowerCase().includes(searchLower))
        return true;

      if (loc.identity && loc.identity.toLowerCase().includes(searchLower))
        return true;

      if (loc.phone && loc.phone.toLowerCase().includes(searchLower))
        return true;

      if (loc.flags) {
        return loc.flags.some(flag =>
          flag && flag.toString().toLowerCase().includes(searchLower)
        );
      }

      return false;
    });
  }, [locations, searchTerm]);

  return (
    <div className="space-y-4">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Search locations by name, address, city, contact, phone..."
          className="pl-9"
        />
      </div>

      {searchTerm.length >= 2 && (
        <div className="text-sm text-muted-foreground">
          {filteredLocations.length} of {locations?.length || 0} locations found
        </div>
      )}

      {filteredLocations && filteredLocations.length > 0 ? (
        <LocationList locations={filteredLocations} onLocationClick={onLocationClick} />
      ) :
        searchTerm.length >= 2 ? (
          <div className="text-center py-8">
            <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No locations found matching "{searchTerm}"</p>
            <p className="text-sm text-muted-foreground mt-2">Try adjusting your search terms</p>
          </div>
        ) : locations && locations.length === 0 ? (
          <p className="text-muted-foreground">No Locations for this business.</p>
        ) : null}
    </div>
  );
}